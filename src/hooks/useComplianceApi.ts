
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { complianceApi, ComplianceDocument } from '@/services/complianceApi';
import { toast } from 'sonner';

// Document submission hook
export const useSubmitDocument = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: complianceApi.submitDocument,
    onSuccess: () => {
      toast.success('Document submitted for compliance check');
      queryClient.invalidateQueries({ queryKey: ['documents'] });
    },
    onError: (error) => {
      toast.error('Failed to submit document: ' + error.message);
    }
  });
};

// Document file upload hook
export const useSubmitDocumentFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ file, documentType }: { file: File; documentType: ComplianceDocument['documentType'] }) =>
      complianceApi.submitDocumentFile(file, documentType),
    onSuccess: () => {
      toast.success('Document uploaded and submitted for compliance check');
      queryClient.invalidateQueries({ queryKey: ['documents'] });
    },
    onError: (error) => {
      toast.error('Failed to upload document: ' + error.message);
    }
  });
};

// KYC submission hook
export const useSubmitKYC = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: complianceApi.submitKYC,
    onSuccess: () => {
      toast.success('KYC processing initiated');
      queryClient.invalidateQueries({ queryKey: ['kyc'] });
    },
    onError: (error) => {
      toast.error('Failed to submit KYC: ' + error.message);
    }
  });
};

// KYC documents upload hook
export const useSubmitKYCDocuments = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ customerId, documents }: { customerId: string; documents: File[] }) =>
      complianceApi.submitKYCDocuments(customerId, documents),
    onSuccess: () => {
      toast.success('KYC documents uploaded successfully');
      queryClient.invalidateQueries({ queryKey: ['kyc'] });
    },
    onError: (error) => {
      toast.error('Failed to upload KYC documents: ' + error.message);
    }
  });
};

// Report generation hook
export const useGenerateReport = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: complianceApi.generateReport,
    onSuccess: () => {
      toast.success('Report generation started');
      queryClient.invalidateQueries({ queryKey: ['reports'] });
    },
    onError: (error) => {
      toast.error('Failed to generate report: ' + error.message);
    }
  });
};

// Regulatory impact analysis hook
export const useAnalyzeRegulatoryImpact = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: complianceApi.analyzeRegulatoryImpact,
    onSuccess: () => {
      toast.success('Regulatory impact analysis started');
      queryClient.invalidateQueries({ queryKey: ['regulatory'] });
    },
    onError: (error) => {
      toast.error('Failed to start impact analysis: ' + error.message);
    }
  });
};

// Workflow status hook
export const useWorkflowStatus = (workflowId: string | null, enabled: boolean = true) => {
  return useQuery({
    queryKey: ['workflow', workflowId, 'status'],
    queryFn: () => workflowId ? complianceApi.getWorkflowStatus(workflowId) : null,
    enabled: enabled && !!workflowId,
    refetchInterval: (query) => {
      // Don't poll if disabled
      if (!enabled) {
        console.log('🛑 Polling disabled for workflow:', workflowId);
        return false;
      }

      // Stop polling if workflow is completed or failed
      const workflowData = query.state.data;
      if (workflowData?.status === 'completed' || workflowData?.status === 'failed') {
        console.log('🏁 Stopping polling - workflow completed with status:', workflowData.status);
        return false;
      }

      // Continue polling every 3 seconds
      console.log('🔄 Continuing to poll workflow status...');
      return 3000;
    },
    refetchOnWindowFocus: false, // Don't refetch when window gains focus
    staleTime: 1000, // Consider data stale after 1 second
  });
};

// Workflow result hook
export const useWorkflowResult = (workflowId: string | null, enabled: boolean = true) => {
  return useQuery({
    queryKey: ['workflow', workflowId, 'result'],
    queryFn: () => workflowId ? complianceApi.getWorkflowResult(workflowId) : null,
    enabled: enabled && !!workflowId
  });
};
