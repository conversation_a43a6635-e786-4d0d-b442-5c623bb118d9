
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Users, Clock, Shield, AlertTriangle, CheckCircle, Search, Upload, FileText } from 'lucide-react';
import { toast } from 'sonner';
import { complianceApi } from '@/services/complianceApi';
import { useSubmitKYC, useSubmitKYCDocuments, useWorkflowStatus } from '@/hooks/useComplianceApi';

const KYCCenter = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [kycQueue, setKycQueue] = useState<any[]>([]);
  const [kycStats, setKycStats] = useState<any>({
    totalProcessed: 0,
    averageTime: 'Loading...',
    automationRate: 0,
    pendingReview: 0
  });
  const [loading, setLoading] = useState(true);

  // KYC Form State
  const [customerForm, setCustomerForm] = useState({
    name: '',
    dateOfBirth: '',
    ssn: '',
    address: '',
    email: '',
    customerType: 'Individual'
  });
  const [selectedDocuments, setSelectedDocuments] = useState<File[]>([]);
  const [currentWorkflowId, setCurrentWorkflowId] = useState<string | null>(null);

  // Hooks
  const submitKYCMutation = useSubmitKYC();
  const submitKYCDocumentsMutation = useSubmitKYCDocuments();
  const { data: workflowStatus } = useWorkflowStatus(currentWorkflowId);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [queue, stats] = await Promise.all([
          complianceApi.getKYCQueue(),
          complianceApi.getKYCStats()
        ]);

        setKycQueue(queue);
        setKycStats(stats);
      } catch (error) {
        console.error('Failed to fetch KYC data:', error);
        // Keep default values on error
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'Low': return 'bg-green-100 text-green-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'High': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'Manual Review Required': return 'bg-red-100 text-red-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  const handleStartKYC = async () => {
    try {
      // Validate form
      if (!customerForm.name || !customerForm.dateOfBirth || !customerForm.ssn || !customerForm.address) {
        toast.error('Please fill in all required fields');
        return;
      }

      // Submit KYC profile
      const result = await submitKYCMutation.mutateAsync({
        personalInfo: {
          name: customerForm.name,
          dateOfBirth: customerForm.dateOfBirth,
          ssn: customerForm.ssn,
          address: customerForm.address
        }
      });

      setCurrentWorkflowId(result.workflowId);

      // If documents are selected, upload them
      if (selectedDocuments.length > 0) {
        await submitKYCDocumentsMutation.mutateAsync({
          customerId: `CUST-${Date.now()}`,
          documents: selectedDocuments
        });
      }

      // Reset form
      setCustomerForm({
        name: '',
        dateOfBirth: '',
        ssn: '',
        address: '',
        email: '',
        customerType: 'Individual'
      });
      setSelectedDocuments([]);

      // Refresh KYC queue
      const queue = await complianceApi.getKYCQueue();
      setKycQueue(queue);

    } catch (error) {
      console.error('KYC submission failed:', error);
    }
  };

  const handleDocumentUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setSelectedDocuments(prev => [...prev, ...files]);
  };

  const removeDocument = (index: number) => {
    setSelectedDocuments(prev => prev.filter((_, i) => i !== index));
  };

  const filteredQueue = kycQueue.filter(item =>
    item.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.id.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* KYC Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Processed</p>
                <p className="text-2xl font-bold text-blue-600">{kycStats.totalProcessed}</p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">This quarter</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Average Time</p>
                <p className="text-2xl font-bold text-green-600">{kycStats.averageTime}</p>
              </div>
              <Clock className="w-8 h-8 text-green-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">90% faster processing</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Automation Rate</p>
                <p className="text-2xl font-bold text-purple-600">{kycStats.automationRate}%</p>
              </div>
              <Shield className="w-8 h-8 text-purple-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">Automated verification</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Review</p>
                <p className="text-2xl font-bold text-orange-600">{kycStats.pendingReview}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-orange-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">Manual intervention needed</p>
          </CardContent>
        </Card>
      </div>

      {/* New KYC Process */}
      <Card>
        <CardHeader>
          <CardTitle>Initiate New KYC Process</CardTitle>
          <CardDescription>
            Start automated customer verification with AI-powered identity validation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Customer Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="customerName">Full Name *</Label>
                <Input
                  id="customerName"
                  placeholder="John Doe"
                  value={customerForm.name}
                  onChange={(e) => setCustomerForm(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="customerEmail">Email Address</Label>
                <Input
                  id="customerEmail"
                  type="email"
                  placeholder="<EMAIL>"
                  value={customerForm.email}
                  onChange={(e) => setCustomerForm(prev => ({ ...prev, email: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="dateOfBirth">Date of Birth *</Label>
                <Input
                  id="dateOfBirth"
                  type="date"
                  value={customerForm.dateOfBirth}
                  onChange={(e) => setCustomerForm(prev => ({ ...prev, dateOfBirth: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="ssn">SSN *</Label>
                <Input
                  id="ssn"
                  placeholder="XXX-XX-XXXX"
                  value={customerForm.ssn}
                  onChange={(e) => setCustomerForm(prev => ({ ...prev, ssn: e.target.value }))}
                />
              </div>
              <div className="md:col-span-2">
                <Label htmlFor="address">Address *</Label>
                <Input
                  id="address"
                  placeholder="123 Main St, City, State, ZIP"
                  value={customerForm.address}
                  onChange={(e) => setCustomerForm(prev => ({ ...prev, address: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="customerType">Customer Type</Label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={customerForm.customerType}
                  onChange={(e) => setCustomerForm(prev => ({ ...prev, customerType: e.target.value }))}
                >
                  <option>Individual</option>
                  <option>Corporate</option>
                  <option>Non-Profit</option>
                </select>
              </div>
            </div>

            {/* Document Upload */}
            <div>
              <Label>Identity Documents</Label>
              <div className="mt-2 border-2 border-dashed border-gray-300 rounded-lg p-4">
                <div className="text-center">
                  <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm text-gray-600 mb-2">
                    Upload identity documents (Driver's License, Passport, etc.)
                  </p>
                  <Button variant="outline" size="sm" onClick={() => document.getElementById('kycFileInput')?.click()}>
                    Choose Files
                  </Button>
                  <input
                    id="kycFileInput"
                    type="file"
                    className="hidden"
                    multiple
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={handleDocumentUpload}
                  />
                </div>

                {selectedDocuments.length > 0 && (
                  <div className="mt-4 space-y-2">
                    {selectedDocuments.map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div className="flex items-center space-x-2">
                          <FileText className="w-4 h-4 text-blue-500" />
                          <span className="text-sm">{file.name}</span>
                          <span className="text-xs text-gray-500">({(file.size / 1024 / 1024).toFixed(1)} MB)</span>
                        </div>
                        <Button variant="ghost" size="sm" onClick={() => removeDocument(index)}>
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <Button
                onClick={handleStartKYC}
                disabled={submitKYCMutation.isPending || submitKYCDocumentsMutation.isPending}
                className="min-w-[200px]"
              >
                {submitKYCMutation.isPending || submitKYCDocumentsMutation.isPending ? (
                  <>
                    <Clock className="w-4 h-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  'Start KYC Process'
                )}
              </Button>
            </div>

            {/* Workflow Status */}
            {workflowStatus && (
              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-blue-900">
                    KYC Processing: {workflowStatus.currentStep}
                  </span>
                  <span className="text-sm text-blue-700">{workflowStatus.progress}%</span>
                </div>
                <div className="w-full bg-blue-200 rounded-full h-2 mb-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${workflowStatus.progress}%` }}
                  ></div>
                </div>
                <p className="text-xs text-blue-600">
                  Status: {workflowStatus.status}
                  {workflowStatus.estimatedCompletion && (
                    <span> • ETA: {new Date(workflowStatus.estimatedCompletion).toLocaleTimeString()}</span>
                  )}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* KYC Queue */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>KYC Processing Queue</CardTitle>
              <CardDescription>
                Real-time status of customer verification workflows
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search customers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 w-64"
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredQueue.map((item) => (
              <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="font-medium">{item.customerName}</h4>
                    <Badge variant="outline">{item.id}</Badge>
                    <Badge className={getRiskColor(item.riskScore)}>
                      {item.riskScore} Risk
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                    <span>Submitted: {item.submissionDate}</span>
                    <span>•</span>
                    <span>Time remaining: {item.timeRemaining}</span>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="flex-1">
                      <div className="flex items-center justify-between text-xs mb-1">
                        <span>Progress: {item.completedSteps}/{item.totalSteps}</span>
                        <span>{Math.round((item.completedSteps / item.totalSteps) * 100)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${(item.completedSteps / item.totalSteps) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    {item.flags.length > 0 && (
                      <div className="flex space-x-1">
                        {item.flags.map((flag: string, index: number) => (
                          <Badge key={index} variant="outline" className="text-xs bg-orange-50 text-orange-700">
                            {flag}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Badge className={getStatusColor(item.status)}>
                    {item.status === 'Completed' && <CheckCircle className="w-3 h-3 mr-1" />}
                    {item.status === 'Manual Review Required' && <AlertTriangle className="w-3 h-3 mr-1" />}
                    {item.status}
                  </Badge>
                  <Button variant="outline" size="sm">
                    View Details
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* KYC Automation Features */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Automated Verification Steps</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <div>
                  <p className="font-medium">Identity Document Verification</p>
                  <p className="text-sm text-gray-600">AI-powered document authenticity checks</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <div>
                  <p className="font-medium">Sanctions Screening</p>
                  <p className="text-sm text-gray-600">Real-time OFAC and global sanctions checks</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <div>
                  <p className="font-medium">Risk Assessment</p>
                  <p className="text-sm text-gray-600">Multi-factor risk scoring and analysis</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <div>
                  <p className="font-medium">PEP & Adverse Media</p>
                  <p className="text-sm text-gray-600">Politically exposed persons screening</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>DBOS Workflow Benefits</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="p-3 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-1">Fault Tolerance</h4>
                <p className="text-sm text-blue-700">Workflows resume automatically from interruption points</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-1">Parallel Processing</h4>
                <p className="text-sm text-green-700">Multiple verification steps run concurrently</p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <h4 className="font-medium text-purple-900 mb-1">Audit Trail</h4>
                <p className="text-sm text-purple-700">Complete workflow history for compliance</p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <h4 className="font-medium text-orange-900 mb-1">Rate Limiting</h4>
                <p className="text-sm text-orange-700">Managed API calls prevent service overload</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default KYCCenter;
